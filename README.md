# Computer Vision (CV) Engineering Coding Exercise

Welcome, and thank you for taking the time to collaborate with us today. The purpose of this exercise is for us to get a sense for how you architect and implement solutions at a technical level and, if successful, will form the basis on further in-person or virtual technical interview topics and discussions. 

# Time
Please plan to spend up to 1-1.5 hours on this challenge.

The intent of the limit is not to time how fast you can produce code, rather to be respectful of your time. 

During the next stage, we'll pair you with another member of the team to continue working on the exercise or digging into new avenues, alternative approaches, or other interesting topics, because we'd much rather see how you work on a problem you're familiar with than on a surprise whiteboard puzzle.

# The Problem 

We have been tracking the movement of a single point on the body (e.g., elbow, wrist, knee) in 3D space in real-time. The positions data is recorded in centimeters relative to the origin (0,0,0).  The tracking is not infalible and the data may sometimes include erroneous outliers due to detection inaccuracies. Your task is to create a mechanism to detect, correct, and report these outliers.

## Task:

1. **Instance Requirement**: Implement a function/object in C++ to process this data in near real-time with a latency of one time step. The function should accept the next data point P<sub>i+1</sub> and return the current point P<sub>i</sub>  with a correction if it is deemed erroneous.

1. **Main Function**: Develop a main function to test your solution. It should process a 1D array where every three values represent the x, y, and z coordinates of the body point at a given time. The function should output the processed data, highlighting corrected points.

## Constraints

- **Erroneous Body Point Definition:** A body point is considered erroneous of the magnitude of the velocity vector between points P<sub>i</sub> and P<sub>i+1</sub> is greater than **3 times** the average magnitude of the velocity vectors from the previous M points (inclusive, from P<sub>i-1</sub> to P<sub>i-M-1</sub>), where M is the size of the sample window (see below). To be clear, velocity vectors should only be computed with consecutive pairs of points, so for M points in the sample window, you should compute M-1 velocity vectors (P<sub>i-1</sub> - P<sub>i-2</sub>, P<sub>i-2</sub> - P<sub>i-3</sub>, …, P<sub>i-M+1</sub> - P<sub>i-M</sub>). Your calculation should be based exclusively on uncorrected points.

- **Sample Window:** The size of the sample window is ideally 5 points, but can be as small as 3 points if fewer than 5 points are present at a given time (i<4). If fewer than 3 points are present (i<2), the sample window is too small to be used for erroneous detection, so your function should return the point as-is rather than attempting to correct it.

- **Data Correction:** If point P<sub>i</sub> is deemed erroneous, correct it by replacing it with the midpoint of P<sub>i-1</sub> and P<sub>i+1</sub>. Your calculation should be based exclusively on uncorrected points.

## Example of what is expected
```python
main():
    # List of body point positions as [x, y, z] for each time frame 
    body_point_positions = [
        10.0, 20.0, 30.0,  # x, y, z at time i=0
        60.0, 70.1, 90.2,  # x, y, z at time i=1
        9.8, 19.9, 29.8,   # x, y, z at time i=2
        100.0, 200.0, 300.0,  # x, y, z at time i=3 (potential outlier)
        10.1, 20.0, 30.1,  # x, y, z at time i=4
        9.9, 19.8, 29.9    # x, y, z at time i=5
    ]

    # <Iterate through positions>
    #    x, y, z at index i = <instance.process>(x, y, z at i+1)
    # <Update/save to final body positions data set>

    # <Print results data>

# Expected Output Example:
# Output Data:
#     10.0, 20.0, 30.0
#     60.0, 70.1, 90.2
#     9.8, 19.9, 29.8
#     9.95, 19.95, 29.95  - Corrected
#     10.1, 20.0, 30.1
#     9.9, 19.8, 29.9
```

## Submitting your solution

Please commit your solution to this repo's `main` branch. You should include both the code you used to solve the problem. If necessary, please also include any instructions for building and running your solution. You may also include any data or validation inputs you created to test your solution with the corresponding outputs.

## FAQ

### What language do I code my solution in?

You are asked to submit your solution using C++ (any standard up to and including C++20)

Solutions in other languages will not be considered.

### Can I use third party libraries or Google as part of my solution?

Yes, and you are encouraged to do so, but give credit where credit is due via citations, if appropriate, and respect licensing agreements of any third-party libraries or packages. 
