#ifndef POINT3D_H
#define POINT3D_H

#include <cmath>

struct Point3D {
    double x, y, z;
    bool corrected = false;

    Point3D(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}

    Point3D midpoint(const Point3D& other) const {
        return Point3D((x + other.x) / 2.0, (y + other.y) / 2.0, (z + other.z) / 2.0);
    }

    double distanceTo(const Point3D& other) const {
        return std::sqrt((x - other.x) * (x - other.x) +
            (y - other.y) * (y - other.y) +
            (z - other.z) * (z - other.z));
    }
};

#endif