﻿#include <iostream>
#include <vector>
#include <iomanip>
#include "Point3D.h"
#include "CheckOutliers.h"

int main() {
    std::vector<double> body_point_positions = {
        10.0, 20.0, 30.0,
        60.0, 70.1, 90.2,
        9.8, 19.9, 29.8,
        100.0, 200.0, 300.0,
        10.1, 20.0, 30.1,
        9.9, 19.8, 29.9
    };

    std::vector<Point3D> inputPoints;
    for (size_t i = 0; i < body_point_positions.size(); i += 3) {
        inputPoints.emplace_back(body_point_positions[i], body_point_positions[i + 1], body_point_positions[i + 2]);
    }

    CheckOutliers corrector;
    std::vector<Point3D> res;

    for (size_t i = 0; i < inputPoints.size() - 1; ++i) {
        Point3D processed = corrector.process(i, inputPoints[i], inputPoints[i + 1]);
        res.push_back(processed);
    }

    res.push_back(inputPoints.back());

    std::cout << "Output Data:\n";
    for (const auto& pt : res) {
        std::cout << std::fixed << std::setprecision(2)
            << "    " << pt.x << ", " << pt.y << ", " << pt.z;
        if (pt.corrected) std::cout << "  - Corrected";
        std::cout << "\n";
    }

    return 0;
}