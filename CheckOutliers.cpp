#include "CheckOutliers.h"

Point3D CheckOutliers::process(int i, const Point3D& Pi, const Point3D& Pi_plus_1) {
    if (i < 2) {
        uncorrectedHistory.push_back(Pi);
        return Pi;
    }

    int windowSize = std::min((int)uncorrectedHistory.size(), maxWindowSize);
    if (windowSize < 3) {
        uncorrectedHistory.push_back(Pi);
        return Pi;
    }

    double velocitySum = 0.0;
    int count = 0;
    for (int j = uncorrectedHistory.size() - 1; j > uncorrectedHistory.size() - windowSize; --j) {
        velocitySum += uncorrectedHistory[j].distanceTo(uncorrectedHistory[j - 1]);
        ++count;
    }

    double avgVelocity = velocitySum / count;
    double currentVelocity = Pi.distanceTo(Pi_plus_1);

    if (currentVelocity > 3.0 * avgVelocity) {
        Point3D corrected = uncorrectedHistory.back().midpoint(Pi_plus_1);
        corrected.corrected = true;
        return corrected;
    }
    else {
        uncorrectedHistory.push_back(Pi);
        return Pi;
    }
}